import { Injectable } from '@angular/core';
import { EvaGoalState } from '../state/eva-goal.state';

@Injectable({ providedIn: 'root' })
export class EvaGoalFacade {
  constructor(private state: EvaGoalState) {}

  /** Signals */
  goalGroups() {
    return this.state.goalGroups();
  }

  measurementScales() {
    return this.state.measurementScales();
  }

  department() {
    return this.state.department();
  }

  employee() {
    return this.state.employee();
  }

  isLoading() {
    return this.state.loading();
  }
} 