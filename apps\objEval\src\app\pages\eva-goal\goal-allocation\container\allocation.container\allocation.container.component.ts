import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { GoalState } from '../../../shared/states/goal.state';
import { ObjSharedModule } from '../../../../../shared/obj-shared.module';
import { FormsModule } from '@angular/forms';
import { AllocationCycleComponent } from '../../components/allocation-cycle/allocation-cycle.component';
import { AllocationDepartmentComponent } from '../../components/allocation-department/allocation-department.component';
import { Location } from '@angular/common';

@Component({
  selector: 'allocation-container',
  imports: [ObjSharedModule, FormsModule, AllocationCycleComponent, AllocationDepartmentComponent],
  templateUrl: './allocation.container.component.html',
  styleUrl: './allocation.container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AllocationContainerComponent implements OnInit {
  protected isCycleAllocation = true;
  protected isPersonDepartmentAllocation = false;

  constructor(private goalState: GoalState, private location: Location) {}

  ngOnInit() {
    console.log(this.goalState.goalAllocation());
  }
  goBack(): void {
    this.location.back();
  }
}
