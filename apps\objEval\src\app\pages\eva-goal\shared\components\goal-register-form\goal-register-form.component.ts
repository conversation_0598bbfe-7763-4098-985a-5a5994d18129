import { ChangeDetectionStrategy, Component, Input, OnInit, ViewChild } from '@angular/core';
import { NzDrawerRef } from 'ng-zorro-antd/drawer';

import { ObjSharedModule } from '../../../../../shared/obj-shared.module';
import { GoalRegisterBasicFormComponent } from './goal-register-basic-form/goal-register-basic-form.component';
import { GoalRegisterCalculatorFormComponent } from './goal-register-calculator-form/goal-register-calculator-form.component';
import { GoalRegisterDataFormComponent } from './goal-register-data-form/goal-register-data-form.component';
import { IGoal } from '../../../goal/models/goal.model';
import { CommonService } from '@hrm-frontend-workspace/core';

@Component({
  selector: 'app-goal-register-form',
  templateUrl: './goal-register-form.component.html',
  styleUrls: ['./goal-register-form.component.scss'],
  imports: [
    GoalRegisterBasicFormComponent,
    GoalRegisterDataFormComponent,
    GoalRegisterCalculatorFormComponent,
    ObjSharedModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GoalRegisterFormComponent implements OnInit {
  /** Dữ liệu truyền vào từ màn hình danh sách */
  @Input() dataItem: any;
  @ViewChild(GoalRegisterBasicFormComponent) basicForm: GoalRegisterBasicFormComponent;
  @ViewChild(GoalRegisterCalculatorFormComponent) calcForm: GoalRegisterCalculatorFormComponent;
  constructor(private drawerRef: NzDrawerRef, private commonService: CommonService) {}

  ngOnInit() {
    console.log(this.dataItem);
  }

  onSubmitForm() {
    if (this.basicForm.isFormInvalid()) {
      return;
    }

    const basic = this.basicForm.getFormValue() as IGoal;
    basic.GoalStatus = 'E_WAITING_APPROVE';
    this.commonService.message({ message: 'Quá buồn', type: 'success' });
    console.log('createGoal payload:', basic);
  }

  close() {
    this.drawerRef.close();
  }
}
