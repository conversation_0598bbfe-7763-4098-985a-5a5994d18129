<div class="form-container">
  <!-- <vnr-setting-config
    keyGroupTranslate="objEval.EvaPerformanceAppraisals"
    [title]="
      'objEval.EvaPerformanceAppraisals.configFormValidator' + (isEdit ? 'Edit' : 'Register')
    "
    [width]="520"
    [top]="18"
    [right]="44"
    [keyScreen]="keyConfig"
  ></vnr-setting-config> -->
  <div class="form-container__body">
    <form
      class="eva-performance-appraisals-form"
      id="evaPerformanceAppraisalsForm"
      #f="ngForm"
      nz-form
      #evaPerformanceAppraisalsFormRef="ngForm"
      [formGroup]="evaPerformanceAppraisalsForm"
      (ngSubmit)="onSubmit()"
    >
      <div nz-row [nzGutter]="8">
        <!-- <PERSON><PERSON><PERSON> giá -->
        <div nz-col class="gutter-row" [nzSm]="{ span: 24 }" [nzXs]="{ span: 24 }">
          <vnr-combobox
            formControlName="EvaluationTypeID"
            [builder]="builderEvaluationType"
            #tplEvaluationType
            (selectDataItem)="onselectDataItem($event, 'EvaluationType')"
            [validatorConfig]="tplEvaluationType"
            [keyGroupBy]="'PerformanceAppraisals'"
            [vnrFormValidator]="evaPerformanceAppraisalsForm"
            [defaultRequired]="true"
          ></vnr-combobox>
        </div>
        <!-- Tên đợt đánh giá -->
        <div nz-col class="gutter-row" [nzSm]="{ span: 24 }" [nzXs]="{ span: 24 }">
          <vnr-input
            formControlName="CycleName"
            [builder]="builderCycleName"
            #tplCycleName
            [validatorConfig]="tplCycleName"
            [keyGroupBy]="'PerformanceAppraisals'"
            [vnrFormValidator]="evaPerformanceAppraisalsForm"
            [defaultRequired]="true"
          ></vnr-input>
        </div>
        <div nz-col class="gutter-row" nzSpan="24">
          <vnr-textarea [builder]="builderDescription" formControlName="Description"></vnr-textarea>
        </div>
        <div nz-col class="gutter-row" nzSpan="24">
          <vnr-daterangepicker
            [builder]="builderPeriodDate"
            formControlName="PeriodDateRange"
            (ngModelChange)="onModelChangeTime($event)"
            #tplPeriodDate
            [validatorConfig]="tplPeriodDate"
            [vnrFormValidator]="evaPerformanceAppraisalsForm"
          ></vnr-daterangepicker>
        </div>
        <!-- Trạng thái -->
        <div nz-col class="gutter-row" [nzSm]="{ span: 24 }" [nzXs]="{ span: 24 }">
          <vnr-combobox
            formControlName="StatusPublic"
            [builder]="builderStatusPublic"
            #tplStatus
            (selectDataItem)="onselectDataItem($event, 'Status')"
            [validatorConfig]="tplStatus"
            [keyGroupBy]="'PerformanceAppraisals'"
            [vnrFormValidator]="evaPerformanceAppraisalsForm"
            [defaultRequired]="true"
          ></vnr-combobox>
        </div>
        <!-- Loại kỳ -->
        <div nz-col class="gutter-row" [nzSm]="{ span: 24 }" [nzXs]="{ span: 24 }">
          <vnr-combobox
            formControlName="PeriodTypeID"
            [builder]="builderPeriodType"
            #tplPeriodTypeID
            (selectDataItem)="onselectDataItem($event, 'PeriodTypeID')"
            [validatorConfig]="tplPeriodTypeID"
            [keyGroupBy]="'PerformanceAppraisals'"
            [vnrFormValidator]="evaPerformanceAppraisalsForm"
            [defaultRequired]="true"
          ></vnr-combobox>
        </div>

        <!-- Đối tượng áp dụng -->
        <div nz-col class="gutter-row" [nzSm]="{ span: 24 }" [nzXs]="{ span: 24 }">
          <vnr-treeview
            [builder]="builderOrg"
            [formGroup]="evaPerformanceAppraisalsForm"
            formControlName="ApplyDepartmentIDs"
          ></vnr-treeview>
        </div>

        <!-- Vị trí áp dụng -->
        <div nz-col class="gutter-row" [nzSm]="{ span: 24 }" [nzXs]="{ span: 24 }">
          <vnr-treeview
            [builder]="builderApplyPosition"
            [formGroup]="evaPerformanceAppraisalsForm"
            formControlName="ApplyPositionIDs"
          ></vnr-treeview>
        </div>
        <!-- Nhân viên áp dụng -->
        <div nz-col class="gutter-row" [nzSm]="{ span: 24 }" [nzXs]="{ span: 24 }">
          <vnr-treeview
            [builder]="builderApplyEmployee"
            [formGroup]="evaPerformanceAppraisalsForm"
            formControlName="ApplyEmployeeIDs"
          ></vnr-treeview>
        </div>
        <div nz-col class="gutter-row" nzSpan="24">
          <vnr-switch
            formControlName="IsAnonymousAppraisals"
            [builder]="builderAnonymousAppraisals"
            #tplAnonymousAppraisals
            [validatorConfig]="tplAnonymousAppraisals"
            [vnrFormValidator]="evaPerformanceAppraisalsForm"
          >
            <span suffix>{{
              'objEval.EvaPerformanceAppraisals.AnonymousAppraisals.label' | translate
            }}</span>
          </vnr-switch>
        </div>
        <div nz-col class="gutter-row" nzSpan="24">
          <vnr-switch
            formControlName="IsRepeat"
            [builder]="builderIsRepeatAppraisals"
            #tplIsRepeat
            [validatorConfig]="tplIsRepeat"
            [vnrFormValidator]="evaPerformanceAppraisalsForm"
          >
            <span suffix>{{
              'objEval.EvaPerformanceAppraisals.IsRepeatAppraisals.label' | translate
            }}</span>
          </vnr-switch>
        </div>
      </div>
    </form>
  </div>
  <div class="form-container__footer">
    <vnr-button
      [vnrIcon]="''"
      [vnrClass]="['--custom-bg']"
      [vnrType]="'default'"
      [vnrText]="'common.btnCancel'"
      (click)="onCancel()"
      [vnrDisabled]="isDisabledBtn"
    >
    </vnr-button>
    <vnr-button
      [vnrIcon]="''"
      [vnrClass]="['--custom-bg']"
      [vnrType]="'primary'"
      [vnrText]="!isEdit ? 'common.btnRegister' : 'common.btnSave'"
      (click)="onSubmit()"
      [vnrDisabled]="isDisabledBtn"
      form="evaPerformanceAppraisalsForm"
      type="submit"
    >
    </vnr-button>
  </div>
</div>
