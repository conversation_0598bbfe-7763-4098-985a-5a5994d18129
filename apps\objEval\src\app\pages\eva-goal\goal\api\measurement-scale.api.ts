import { Inject, Injectable, Injector } from '@angular/core';
import { VnrResourceService } from '@hrm-frontend-workspace/common';
import { APP_CONFIG, IAppConfiguration } from '@hrm-frontend-workspace/models';
import { IMeasurementScale } from '../models/measurement-scale.model';
import { Observable, of } from 'rxjs';
import { measurementScaleMockData } from '../data/all-mock.data';

@Injectable()
export class MeasurementScaleApi extends VnrResourceService<IMeasurementScale> {
  constructor(
    @Inject(APP_CONFIG) protected appConfig: IAppConfiguration,
    protected injector: Injector,
  ) {
    super(appConfig.API_URL, injector);
  }

  getResource(): string {
    return 'eva-measurement-scales';
  }

  public getMeasurementScales(): Observable<IMeasurementScale[]> {
    // Todo: replace with real HTTP call when backend ready
    return of(measurementScaleMockData);
  }
} 