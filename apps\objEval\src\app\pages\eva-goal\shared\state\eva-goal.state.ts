import { Injectable, computed, effect } from '@angular/core';
import { signal } from '@angular/core';
import { BuilderDataService } from '../service/builder-data.service';

@Injectable({ providedIn: 'root' })
export class EvaGoalState {
  constructor(private builderData: BuilderDataService) {
    effect(() => {
      if (this.goalGroups().length && this.measurementScales().length && this.department().length && this.employee().length) {
        this.loading.set(false);
      }
    });
  }

  /** trạng thái loading khởi tạo */
  readonly loading = signal<boolean>(true);

  /** danh sách nhóm mục tiêu đã mapping */
  readonly goalGroups = computed(() => this.builderData.goalGroupsSig());

  /** danh sách thang đo */
  readonly measurementScales = computed(() => this.builderData.measurementScalesSig());

  /** danh sách phòng ban */
  readonly department = computed(() => this.builderData.departmentSig());

  /** danh sách nhân viên */
  readonly employee = computed(() => this.builderData.employeeSig());
} 