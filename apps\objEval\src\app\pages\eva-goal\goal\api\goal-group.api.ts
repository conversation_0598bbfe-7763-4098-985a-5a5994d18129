import { Inject, Injectable, Injector } from '@angular/core';
import { VnrResourceService } from '@hrm-frontend-workspace/common';
import { APP_CONFIG, IAppConfiguration } from '@hrm-frontend-workspace/models';
import { IGoalGroup } from '../models/goal-group.model';
import { of, Observable } from 'rxjs';
import { goalGroupMockData } from '../data/all-mock.data';

@Injectable()
export class GoalGroupApi extends VnrResourceService<IGoalGroup> {
  constructor(
    @Inject(APP_CONFIG) protected appConfig: IAppConfiguration,
    protected injector: Injector,
  ) {
    super(appConfig.API_URL, injector);
  }

  getResource(): string {
    return 'eva-goal-groups';
  }

  public getGoalGroups(): Observable<IGoalGroup[]> {
    return of(goalGroupMockData);
  }
} 