import { ChangeDetectionStrategy, Component, Input, OnInit, effect } from '@angular/core';
import { UntypedFormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  VnrTextBoxBuilder,
  VnrComboBoxBuilder,
  VnrInputNumberBuilder,
  VnrRadioButtonBuilder,
  VnrDateRangePickerBuilder,
} from '@hrm-frontend-workspace/vnr-module';
import { NzDrawerRef } from 'ng-zorro-antd/drawer';
import { BuilderDataService } from '../../../service/builder-data.service';
import { ObjSharedModule } from '../../../../../../shared/obj-shared.module';
import { DynamicValidateJsonService } from '@hrm-frontend-workspace/ui';

@Component({
  selector: 'app-goal-register-basic-form',
  templateUrl: './goal-register-basic-form.component.html',
  styleUrls: ['./goal-register-basic-form.component.scss'],
  standalone: true,
  imports: [ObjSharedModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GoalRegisterBasicFormComponent implements OnInit {
  @Input() data: any;

  protected form: FormGroup;
  protected goalTypeBuilder: VnrRadioButtonBuilder = new VnrRadioButtonBuilder();
  protected kpiTargetBuilder: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected goalCodeBuilder: VnrTextBoxBuilder = new VnrTextBoxBuilder();
  protected goalNameBuilder: VnrTextBoxBuilder = new VnrTextBoxBuilder();
  protected goalGroupBuilder: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected measurementTypeBuilder: VnrRadioButtonBuilder = new VnrRadioButtonBuilder();
  protected targetValueBuilder: VnrInputNumberBuilder = new VnrInputNumberBuilder();
  protected unitBuilder: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected weightBuilder: VnrInputNumberBuilder = new VnrInputNumberBuilder();
  protected periodTimeBuilder: VnrDateRangePickerBuilder = new VnrDateRangePickerBuilder();
  protected parentGoalBuilder: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected departmentBuilder: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected representativeBuilder: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected objectiveBuilder: VnrRadioButtonBuilder = new VnrRadioButtonBuilder();

  protected kpiTargetDataSource = [
    { value: 'DT001', text: 'Mục tiêu doanh số' },
    { value: 'DT002', text: 'Mục tiêu lợi nhuận' },
    { value: 'DT003', text: 'Mục tiêu số lượng khách hàng mới' },
    { value: 'DT004', text: 'Mục tiêu tỷ lệ chuyển đổi' },
  ];

  protected unitDataSource = [
    { value: 'VND', text: 'VNĐ' },
    { value: 'PERCENT', text: '%' },
    { value: 'NUMBER', text: 'Số lượng' },
    { value: 'HOUR', text: 'Giờ' },
  ];

  protected goalGroupDataSource = [];
  protected measurementScaleDataSource = [];
  protected departmentDataSource = [];
  protected employeeDataSource = [];
  // Đồng bộ dữ liệu signal -> biến mảng cho template
  private _syncEffect = effect(() => {
    this.measurementScaleDataSource = this.builderData.measurementScalesSig();
    this.goalGroupDataSource = this.builderData.goalGroupsSig();
    this.departmentDataSource = this.builderData.departmentSig();
    this.employeeDataSource = this.builderData.employeeSig();
  });

  constructor(
    private fb: UntypedFormBuilder,
    private dynamicValidateJsonService: DynamicValidateJsonService,
    private builderData: BuilderDataService,
  ) {}

  ngOnInit() {
    this.dynamicValidateJsonService.setAllowOrder(true);
    this.initForm();
    this.initBuilders();

    if (this.data) {
      // Xử lý dữ liệu ngày tháng cho DateRangePicker
      const periodTimeValue = this.data?.periodTime
        ? Array.isArray(this.data.periodTime)
          ? this.data.periodTime
          : [this.data.periodTime, null]
        : null;

      this.form.patchValue({
        GoalType: this.data?.goalType || 'KPI',
        kpiTarget: this.data?.kpiTarget || null,
        GoalCode: this.data?.goalCode || '',
        GoalName: this.data?.goalName || '',
        goalGroup: this.data?.goalGroup || null,
        measurementType: this.data?.measurementType || 'TỐI ĐA',
        targetValue: this.data?.targetValue || 0,
        unit: this.data?.unit || null,
        weight: this.data?.weight || 0,
        periodTime: periodTimeValue,
        ParentID: this.data?.parentGoal || null,
        AssignmentScope: this.data?.objective || null,
        department: this.data?.department || null,
        representative: this.data?.representative || null,
      });
    }
  }

  initForm() {
    this.form = this.fb.group({
      GoalType: [null, [Validators.required]],
      kpiTarget: [null, [Validators.required]],
      GoalCode: [null, [Validators.required]],
      GoalName: [null, [Validators.required]],
      goalGroup: [null, [Validators.required]],
      measurementType: [null, [Validators.required]],
      targetValue: [0, [Validators.required]],
      unit: [null, [Validators.required]],
      weight: [0, [Validators.required, Validators.min(0), Validators.max(100)]],
      periodTime: [null, [Validators.required]],
      ParentID: [null],
      AssignmentScope: [null, [Validators.required]],
      department: [null, [Validators.required]],
      representative: [null, [Validators.required]],
    });
  }

  initBuilders() {
    this.goalTypeBuilder.builder({
      label: 'Loại mục tiêu',
      dataSource: [
        { value: 'KPI', label: 'KPI' },
        { value: 'OKR', label: 'OKR' },
      ],
      textField: 'label',
      valueField: 'value',
      required: true,
    });

    this.kpiTargetBuilder.builder({
      label: 'Chọn từ kho chỉ tiêu',
      textField: 'text',
      valueField: 'value',
      placeholder: 'Chọn chỉ tiêu',
      required: true,
    });

    this.goalCodeBuilder.builder({
      label: 'Mã mục tiêu',
      placeholder: 'Nhập mã mục tiêu',
      required: true,
    });

    this.goalNameBuilder.builder({
      label: 'Tên mục tiêu',
      placeholder: 'Nhập tên mục tiêu',
      required: true,
    });

    this.goalGroupBuilder.builder({
      label: 'Nhóm mục tiêu',
      textField: 'text',
      valueField: 'value',
      placeholder: 'Chọn nhóm mục tiêu',
      required: true,
    });
    this.measurementTypeBuilder.builder({
      label: 'Thang đo mục tiêu',
      dataSource: [
        { value: 'TỐI ĐA', label: 'Tối đa' },
        { value: 'TỐI THIỂU', label: 'Tối thiểu' },
        { value: 'TRONG KHOẢNG', label: 'Trong khoảng' },
        { value: 'KHÔNG ĐO', label: 'Không đo' },
      ],
      textField: 'label',
      valueField: 'value',
      required: true,
    });

    this.targetValueBuilder.builder({
      label: 'Giá trị tối thiểu',
      placeholder: '0',
      required: true,
      options: {
        format: '#,###.##',
        min: 0,
        max: 100000000000000,
      },
    });

    this.unitBuilder.builder({
      label: 'Đơn vị đo',
      textField: 'text',
      valueField: 'value',
      placeholder: 'Chọn đơn vị đo',
      required: false,
    });

    this.weightBuilder.builder({
      label: 'Trọng số',
      placeholder: '0',
      required: false,
      options: {
        format: '#,###.##',
        min: 0,
        max: 100,
      },
    });

    this.periodTimeBuilder.builder({
      label: 'Chu kỳ thực hiện',
      placeholder: ['Từ ngày', 'Đến ngày'],
      required: false,
      options: {
        format: 'dd/MM/yyyy',
      },
    });

    this.parentGoalBuilder.builder({
      label: 'Liên kết mục tiêu cha',
      textField: 'text',
      valueField: 'value',
      placeholder: 'Chọn mục tiêu cha',
      required: false,
    });

    this.departmentBuilder.builder({
      label: 'Đơn vị phòng ban',
      textField: 'DepartmentName',
      valueField: 'DepartmentCode',
      placeholder: 'Chọn đơn vị phòng ban',
      required: false,
    });

    this.representativeBuilder.builder({
      label: 'Người đại diện',
      textField: 'EmployeeName',
      valueField: 'EmployeeCode',
      placeholder: 'Chọn người đại diện',
      required: true,
    });

    this.objectiveBuilder.builder({
      label: 'Đối tượng thực hiện',
      dataSource: [
        { value: 'unit', label: 'Đơn vị' },
        { value: 'person', label: 'Cá nhân' },
      ],
      textField: 'label',
      valueField: 'value',
      required: true,
    });
  }

  getFormValue() {
    return this.form.value;
  }

  isFormValid(): boolean {
    return this.form.valid;
  }

  isFormInvalid(): boolean {
    Object.values(this.form.controls).forEach((control) => {
      if (control.invalid) {
        control.markAsDirty();
        control.markAsTouched();
        control.updateValueAndValidity({ onlySelf: true });
      }
    });
    if (this.form.invalid) {
      return true;
    }
    return false;
  }
}
