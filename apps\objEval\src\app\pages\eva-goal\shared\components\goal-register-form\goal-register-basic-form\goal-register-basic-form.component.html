<form nz-form [formGroup]="form">
  <!-- <PERSON><PERSON><PERSON> mục tiêu -->
  <vnr-radiobutton formControlName="GoalType" [builder]="goalTypeBuilder"></vnr-radiobutton>
  <!-- Chọn từ kho chỉ tiêu -->
  <vnr-combobox
    formControlName="kpiTarget"
    [builder]="kpiTargetBuilder"
    [vnrDataSource]="kpiTargetDataSource"
    #tplKpiTarget
    [validatorConfig]="tplKpiTarget"
    [vnrFormValidator]="form"
  ></vnr-combobox>

  <!-- Mã mục tiêu -->
  <vnr-input
    formControlName="GoalCode"
    [builder]="goalCodeBuilder"
    #tplGoalCode
    [validatorConfig]="tplGoalCode"
    [vnrFormValidator]="form"
  ></vnr-input>

  <!-- Tên mục tiêu -->
  <vnr-input
    formControlName="GoalName"
    [builder]="goalNameBuilder"
    #tplGoalName
    [validatorConfig]="tplGoalName"
    [vnrFormValidator]="form"
  ></vnr-input>

  <!-- Nhóm mục tiêu -->
  <vnr-combobox
    formControlName="goalGroup"
    [builder]="goalGroupBuilder"
    [vnrDataSource]="goalGroupDataSource"
    #tplGoalGroup
    [validatorConfig]="tplGoalGroup"
    [vnrFormValidator]="form"
  ></vnr-combobox>
  <vnr-radiobutton
    formControlName="measurementType"
    [builder]="measurementTypeBuilder"
  ></vnr-radiobutton>
  <!-- Giá trị tối thiểu và Đơn vị đo -->
  <nz-row [nzGutter]="16">
    <nz-col [nzSpan]="12">
      <vnr-inputnumber
        formControlName="targetValue"
        [builder]="targetValueBuilder"
        #tplTargetValue
        [validatorConfig]="tplTargetValue"
        [vnrFormValidator]="form"
      ></vnr-inputnumber>
    </nz-col>
    <nz-col [nzSpan]="12">
      <vnr-combobox
        formControlName="unit"
        [builder]="unitBuilder"
        [vnrDataSource]="unitDataSource"
        #tplUnit
        [validatorConfig]="tplUnit"
        [vnrFormValidator]="form"
      ></vnr-combobox>
    </nz-col>
  </nz-row>

  <!-- Trọng số -->
  <vnr-inputnumber
    formControlName="weight"
    [builder]="weightBuilder"
    #tplWeight
    [validatorConfig]="tplWeight"
    [vnrFormValidator]="form"
  ></vnr-inputnumber>

  <!-- Chu kỳ thực hiện -->
  <vnr-daterangepicker
    formControlName="periodTime"
    [builder]="periodTimeBuilder"
    #tplPeriodTime
    [validatorConfig]="tplPeriodTime"
    [vnrFormValidator]="form"
  ></vnr-daterangepicker>
  <!-- Liên kết mục tiêu cha -->
  <vnr-combobox
    formControlName="ParentID"
    [builder]="parentGoalBuilder"
    #tplParentGoal
    [validatorConfig]="tplParentGoal"
    [vnrFormValidator]="form"
  ></vnr-combobox>
  <!-- Đối tượng thực hiện-->
  <vnr-radiobutton
    formControlName="AssignmentScope"
    [builder]="objectiveBuilder"
    #tplObjective
    [validatorConfig]="tplObjective"
    [vnrFormValidator]="form"
  ></vnr-radiobutton>
  <!-- Đơn vị phòng ban -->
  <vnr-combobox
    formControlName="department"
    [vnrDataSource]="departmentDataSource"
    [builder]="departmentBuilder"
    #tplDepartment
    [validatorConfig]="tplDepartment"
    [vnrFormValidator]="form"
  ></vnr-combobox>
  <!-- Người đại diện -->
  <vnr-combobox
    formControlName="representative"
    [vnrDataSource]="employeeDataSource"
    [builder]="representativeBuilder"
    #tplRepresentative
    [validatorConfig]="tplRepresentative"
    [vnrFormValidator]="form"
  ></vnr-combobox>
</form>
