import { Component, ViewChild } from '@angular/core';
import { TabFilterSchema } from '@hrm-frontend-workspace/ui';
import { NzDrawerService } from 'ng-zorro-antd/drawer';

import { ObjSharedModule } from '../../../../shared/obj-shared.module';
import { GoalDepartmentListComponent } from '../components/goal-department/goal-department-list/goal-department-list.component';
import { GoalDepartmentTableComponent } from '../components/goal-department/goal-department-table/goal-department-table.component';
import { GoalPersonalListComponent } from '../components/goal-personal/goal-personal-list/goal-personal-list.component';
import { GoalPersonalTableComponent } from '../components/goal-personal/goal-personal-table/goal-personal-table.component';
import { GoalStatusFilterData, GoalTabData } from '../../shared/enums/menu.data';
import { CategoriesFacade } from '../facade/categories.facade';
import { TabNavEnum } from '../models/goal-period.model';
import { GoalDetailComponent } from '../../goal-detail/container/goal-detail.component';
@Component({
  selector: 'app-goal-period',
  templateUrl: './goal-period.component.html',
  styleUrls: ['./goal-period.component.scss'],
  imports: [
    ObjSharedModule,
    GoalDepartmentListComponent,
    GoalDepartmentTableComponent,
    GoalPersonalListComponent,
    GoalPersonalTableComponent,
  ],
})
export class GoalPeriodComponent {
  @ViewChild('GoalDepartmentList', { static: true })
  GoalDepartmentList: GoalDepartmentListComponent;
  @ViewChild('GoalPersonalList', { static: true })
  GoalPersonalList: GoalPersonalListComponent;
  protected viewMode = 'list';
  protected GoalTabData: TabFilterSchema[] = GoalTabData;
  protected GoalStatusFilterData: TabFilterSchema[] = GoalStatusFilterData;
  protected tabMode = 'department';
  protected hideTabAction = false;
  protected tabCount;
  protected title = 'objEval.GoalPeriod.Title';

  constructor(private categoriesFacade: CategoriesFacade, private drawerService: NzDrawerService) {}

  protected onViewModeChange(value: string) {
    this.viewMode = value;
  }

  protected onTabChange(value) {
    if (value === TabNavEnum.DEPARTMENT) {
      this.tabMode = 'department';
      this.hideTabAction = false;
    } else if (value === TabNavEnum.PERSONAL) {
      this.tabMode = 'personal';
      this.hideTabAction = false;
    }
    this.viewMode = 'list';
  }
  protected onNodeClick(event: any) {
    this.drawerService.create({
      nzTitle: null,
      nzClosable: false,
      nzContent: GoalDetailComponent,
      nzWidth: 1200,
      nzMaskClosable: false,
      nzMask: true,
      nzContentParams: {
        dataItem: event,
      },
    });
  }
}
