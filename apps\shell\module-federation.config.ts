import { ModuleFederationConfig } from '@nx/module-federation';

const config: ModuleFederationConfig = {
  name: 'shell',
  remotes: ['dashboard', 'objEval'],
  shared: (library) => {
    return {
      singleton: true,
      strictVersion: false,
      eager: true,
      requiredVersion: false,
    };
  },
};

/**
 * Nx requires a default export of the config to allow correct resolution of the module federation graph.
 **/
export default config;
