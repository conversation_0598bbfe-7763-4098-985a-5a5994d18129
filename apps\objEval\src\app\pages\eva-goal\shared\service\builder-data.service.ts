import { Injectable } from '@angular/core';
import { GoalGroupApi } from '../../goal/api/goal-group.api';
import { MeasurementScaleApi } from '../../goal/api/measurement-scale.api';
import { toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs/operators';
import { IGoalGroup } from '../../goal/models/goal-group.model';
import { IMeasurementScale } from '../../goal/models/measurement-scale.model';
import { EvaCommonApi } from '../api/eva-common.api';

@Injectable({ providedIn: 'root' })
export class BuilderDataService {
  constructor(
    private goalGroupApi: GoalGroupApi,
    private measurementScaleApi: MeasurementScaleApi,
    private evaCommonApi: EvaCommonApi,
  ) {}

  /** Danh sách nhóm mục tiêu (đã mapping text/value) */
  readonly goalGroupsSig = toSignal(
    this.goalGroupApi
      .getGoalGroups()
      .pipe(map((raw: IGoalGroup[]) => raw.map((g) => ({ value: g.Id, text: g.GroupName })))),
    { initialValue: [] as Array<{ value: string; text: string }> },
  );

  /** Danh sách thang đo */
  readonly measurementScalesSig = toSignal(
    this.measurementScaleApi.getMeasurementScales(),
    { initialValue: [] as IMeasurementScale[] },
  );

  /** Danh sách phòng ban */
  readonly departmentSig = toSignal(
    this.evaCommonApi.getDepartment(),
    { initialValue: [] as any[] },
  );

  /** Danh sách nhân viên */
  readonly employeeSig = toSignal(
    this.evaCommonApi.getEmployee(),
    { initialValue: [] as any[] },
  );
} 