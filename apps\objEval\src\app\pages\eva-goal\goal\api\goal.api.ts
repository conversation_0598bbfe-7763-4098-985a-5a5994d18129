import { Inject, Injectable, Injector } from '@angular/core';
import { VnrResourceService } from '@hrm-frontend-workspace/common';
import { APP_CONFIG, IAppConfiguration } from '@hrm-frontend-workspace/models';
import { IGoal } from '../models/goal.model';
import { Observable } from 'rxjs';

@Injectable()
export class GoalApi extends VnrResourceService<IGoal> {
  constructor(
    @Inject(APP_CONFIG) protected appConfig: IAppConfiguration,
    protected injector: Injector,
  ) {
    super(appConfig.API_URL, injector);
  }

  getResource(): string {
    return 'api/eva_goal';
  }

  /** <PERSON><PERSON><PERSON> danh sách mục tiêu */
  getList(): Observable<IGoal[]> {
    return this.vnrSendRequest('GET', this.appConfig.API_URL + this.getResource() + '/GetList', {
      isNoNoti: true,
    });
  }

  /** Tạo mục tiêu mới */
  create(goal: IGoal): Observable<any> {
    return this.vnrSendRequest('POST', this.appConfig.API_URL + this.getResource() + '/Create', {
      body: goal,
    });
  }

  /** Cập nhật mục tiêu */
  update(goal: IGoal): Observable<any> {
    return this.vnrSendRequest('POST', this.appConfig.API_URL + this.getResource() + '/Update', {
      body: goal,
    });
  }

  /** Xoá mục tiêu */
  delete(ids: string[]): Observable<any> {
    return this.vnrSendRequest('POST', this.appConfig.API_URL + this.getResource() + '/Delete', {
      body: ids,
    });
  }
} 