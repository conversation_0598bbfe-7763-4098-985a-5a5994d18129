import { Inject, Injectable, Injector } from '@angular/core';
import { VnrResourceService } from '@hrm-frontend-workspace/common';
import { APP_CONFIG, IAppConfiguration } from '@hrm-frontend-workspace/models';
import { Observable, of } from 'rxjs';
import { departmentMockData, employeeMockData } from '../data/all-mock.data';

@Injectable()
export class EvaCommonApi extends VnrResourceService<any> {
  constructor(
    @Inject(APP_CONFIG) protected appConfig: IAppConfiguration,
    protected injector: Injector,
  ) {
    super(appConfig.SERVICE_API_URL, injector);
  }

  getResource(): string {
    return 'api/eva_common';
  }

  getDepartment(): Observable<any[]> {
    // return this.vnrSendRequest(
    //   'POST',
    //   this.appConfig.API_URL + this.getResource() + '/GetDepartment',
    //   {
    //     isNoNoti: true,
    //   },
    // );
    return of(departmentMockData);
  }

  getEmployee(): Observable<any[]> {
    return of(employeeMockData);
  }
}
